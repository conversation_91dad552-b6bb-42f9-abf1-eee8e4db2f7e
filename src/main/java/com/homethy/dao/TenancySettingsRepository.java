package com.homethy.dao;

import com.homethy.model.TenancySettings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description TenancySettings repository
 */
@Repository
public interface TenancySettingsRepository extends JpaRepository<TenancySettings, String> {

    /**
     * Find tenancy settings by ID and not deleted
     */
    @Query("SELECT ts FROM TenancySettings ts WHERE ts.id = :id AND ts.deleteFlag = 0")
    Optional<TenancySettings> findByIdAndNotDeleted(@Param("id") String id);

    /**
     * Find tenancy settings by tenancy ID and not deleted
     */
    @Query("SELECT ts FROM TenancySettings ts WHERE ts.tenancySettingsTenancyId = :tenancyId AND ts.deleteFlag = 0")
    Optional<TenancySettings> findByTenancyIdAndNotDeleted(@Param("tenancyId") String tenancyId);
}
