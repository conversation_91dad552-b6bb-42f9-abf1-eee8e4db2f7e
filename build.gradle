plugins {
    id 'java'
    id 'org.springframework.boot' version '2.7.14'
    id 'io.spring.dependency-management' version '1.1.0'
    id 'checkstyle'
    id 'com.github.spotbugs' version '5.0.13'
}

group = 'com.homethy'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '11'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    
    // Database
    implementation 'mysql:mysql-connector-java:8.0.33'
    
    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'
    
    // JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4.2'
    
    // Utility libraries
    implementation 'cn.hutool:hutool-all:5.8.25'
    implementation 'com.google.guava:guava:33.0.0-jre'
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    
    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.jmockit:jmockit:1.49'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2022.0.3"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// Checkstyle configuration
checkstyle {
    toolVersion = '10.3.1'
    ignoreFailures = true
    maxWarnings = 100
}

// SpotBugs configuration
spotbugs {
    ignoreFailures = true
    effort = 'max'
    reportLevel = 'low'
}

// Java compilation options
compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs += ['-Xlint:unchecked', '-Xlint:deprecation']
}

compileTestJava {
    options.encoding = 'UTF-8'
}

// JAR configuration
jar {
    enabled = false
}

bootJar {
    archiveFileName = "${project.name}-${project.version}.jar"
}


