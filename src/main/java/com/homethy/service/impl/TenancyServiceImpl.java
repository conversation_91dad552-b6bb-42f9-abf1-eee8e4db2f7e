package com.homethy.service.impl;

import com.homethy.dao.TenancyRepository;
import com.homethy.dao.TenancySettingsRepository;
import com.homethy.enums.ErrorCodeEnum;
import com.homethy.enums.TenancyStatus;
import com.homethy.exception.ListingHubException;
import com.homethy.model.Tenancy;
import com.homethy.model.TenancySettings;
import com.homethy.model.dto.TenancyResponse;
import com.homethy.service.TenancyService;
import com.homethy.util.BeanCopyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Tenancy service implementation
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TenancyServiceImpl implements TenancyService {

    private final TenancyRepository tenancyRepository;
    private final TenancySettingsRepository tenancySettingsRepository;

    @Override
    @Transactional
    public TenancyResponse copyTenancy(String organisationId, String tenancyId) {
        try {
            log.info("Starting to copy tenancy {} for organisation {}", tenancyId, organisationId);

            // Get the original tenancy
            Tenancy originalTenancy = tenancyRepository.findByIdAndNotDeleted(tenancyId)
                    .orElseThrow(() -> new ListingHubException(ErrorCodeEnum.TENANCY_NOT_FOUND));

            // Verify the tenancy belongs to the organisation
            if (!organisationId.equals(originalTenancy.getTenancyOrganisationId())) {
                throw new ListingHubException(ErrorCodeEnum.TENANCY_NOT_FOUND);
            }

            // Get the original tenancy settings if exists
            TenancySettings originalTenancySettings = null;
            if (originalTenancy.getTenancySettingsId() != null) {
                originalTenancySettings = tenancySettingsRepository
                        .findByIdAndNotDeleted(originalTenancy.getTenancySettingsId())
                        .orElse(null);
            }

            LocalDateTime now = LocalDateTime.now();
            String copyTenancyId = UUID.randomUUID().toString();
            String copyTenancySettingsId = UUID.randomUUID().toString();

            // Create new tenancy settings if original exists
            if (originalTenancySettings != null) {
                TenancySettings newTenancySettings = createCopyTenancySettings(
                        originalTenancySettings, copyTenancySettingsId, copyTenancyId, now);
                tenancySettingsRepository.save(newTenancySettings);
                log.info("Created new tenancy settings with ID: {}", copyTenancySettingsId);
            }

            // Create new tenancy data
            Tenancy newTenancy = buildCopyTenancyData(originalTenancy, copyTenancySettingsId, copyTenancyId, now);

            // Generate unique reference
            String newReference = generateUniqueReference(originalTenancy.getReference(), organisationId);
            newTenancy.setReference(newReference);

            // Save the new tenancy
            Tenancy savedTenancy = tenancyRepository.save(newTenancy);

            log.info("Successfully copied tenancy {} to new tenancy {}", tenancyId, savedTenancy.getId());

            // Convert to response DTO
            return BeanCopyUtil.copyProperties(savedTenancy, TenancyResponse.class);

        } catch (ListingHubException e) {
            log.error("Business error copying tenancy {}: {}", tenancyId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error copying tenancy {}: {}", tenancyId, e.getMessage(), e);
            throw new ListingHubException(ErrorCodeEnum.TENANCY_COPY_FAILED);
        }
    }

    /**
     * Create a copy of tenancy settings
     */
    private TenancySettings createCopyTenancySettings(TenancySettings original, String newId, String newTenancyId, LocalDateTime now) {
        TenancySettings copy = BeanCopyUtil.copyProperties(original, TenancySettings.class);
        copy.setId(newId);
        copy.setTenancySettingsTenancyId(newTenancyId);
        copy.setCreateTime(now);
        copy.setUpdateTime(now);
        copy.setDeleteFlag(0);
        return copy;
    }

    /**
     * Build copy tenancy data
     */
    private Tenancy buildCopyTenancyData(Tenancy original, String tenancySettingsId, String tenancyId, LocalDateTime now) {
        Tenancy copy = BeanCopyUtil.copyProperties(original, Tenancy.class);
        copy.setId(tenancyId);
        copy.setTenancySettingsId(tenancySettingsId);
        copy.setCreateTime(now);
        copy.setUpdateTime(now);
        copy.setStatus(TenancyStatus.DRAFT);
        copy.setDeleteFlag(0);
        return copy;
    }

    /**
     * Generate a unique reference for the copied tenancy
     * Logic: Check if current reference ends with -counter, if yes then +1, if no then add -1
     */
    private String generateUniqueReference(String originalReference, String organisationId) {
        try {
            log.info("Generating unique reference for original: {}", originalReference);

            // Get all existing tenancies for the organisation to check for existing references
            List<Tenancy> existingTenancies = tenancyRepository.findByOrganisationIdAndNotDeleted(organisationId);

            // Simple logic: split by last '-' and check if the last part is a number
            int lastDashIndex = originalReference.lastIndexOf('-');

            String baseReference;
            int startCounter;

            if (lastDashIndex != -1) {
                String beforeDash = originalReference.substring(0, lastDashIndex);
                String afterDash = originalReference.substring(lastDashIndex + 1);

                // Check if the part after the last dash is a number
                if (Pattern.matches("\\d+", afterDash)) {
                    // It's a number, so increment it
                    baseReference = beforeDash;
                    startCounter = Integer.parseInt(afterDash) + 1;
                    log.info("Found counter: {}, incrementing to {}", afterDash, startCounter);
                } else {
                    // Not a number, treat whole reference as base and add -1
                    baseReference = originalReference;
                    startCounter = 1;
                    log.info("No counter found, adding -1 to {}", originalReference);
                }
            } else {
                // No dash found, treat whole reference as base and add -1
                baseReference = originalReference;
                startCounter = 1;
                log.info("No dash found, adding -1 to {}", originalReference);
            }

            // Find the next available counter
            int counter = startCounter;
            String newReference = baseReference + "-" + counter;

            while (tenancyRepository.existsByReferenceAndOrganisationId(newReference, organisationId)) {
                counter++;
                newReference = baseReference + "-" + counter;
            }

            log.info("Final generated reference: {}", newReference);
            return newReference;

        } catch (Exception e) {
            // Fallback to timestamp-based reference if generation fails
            log.error("Failed to generate unique reference, using timestamp fallback: {}", e.getMessage(), e);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmmss"));
            return originalReference + "-" + timestamp;
        }
    }
}
