package com.homethy.model;

import com.homethy.enums.FeeType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description TenancySettings entity
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tenancy_settings")
public class TenancySettings extends BaseEntity {

    @Column(name = "tenancy_settings_tenancy_id", nullable = false)
    private String tenancySettingsTenancyId;

    @Column(name = "currency", nullable = false)
    private String currency;

    @Column(name = "invoice_rent_in_advance_days", nullable = false)
    private Integer invoiceRentInAdvanceDays;

    @Column(name = "auto_invoice_rent", nullable = false)
    private Boolean autoInvoiceRent;

    @Column(name = "organisation_id", nullable = false)
    private String organisationId;

    @Column(name = "rent_commission", precision = 10, scale = 2)
    private BigDecimal rentCommission;

    @Column(name = "percentage_fee", precision = 10, scale = 2)
    private BigDecimal percentageFee;

    @Column(name = "fixed_fee", precision = 10, scale = 2)
    private BigDecimal fixedFee;

    @Enumerated(EnumType.STRING)
    @Column(name = "fee_type")
    private FeeType feeType;

    @Column(name = "tenancy_ledger_code")
    private String tenancyLedgerCode;

    @Column(name = "account_code")
    private String accountCode;

    @Column(name = "fee_ledger_code")
    private String feeLedgerCode;

    @Column(name = "next_invoice_send_date")
    private LocalDateTime nextInvoiceSendDate;

    @Column(name = "next_invoice_due_date")
    private LocalDateTime nextInvoiceDueDate;

    @Column(name = "send_invoice_to_tenant")
    private Boolean sendInvoiceToTenant;

    @Column(name = "stationary")
    private String stationary;

    @Column(name = "contract_value", precision = 10, scale = 2)
    private BigDecimal contractValue;

    @Column(name = "erv", precision = 10, scale = 2)
    private BigDecimal eRV;

    @Column(name = "first_journal_run_date")
    private LocalDateTime firstJournalRunDate;

    @Column(name = "last_journal_run_date")
    private LocalDateTime lastJournalRunDate;

    @Column(name = "auto_approval_bill")
    private Boolean autoApprovalBill;
}
