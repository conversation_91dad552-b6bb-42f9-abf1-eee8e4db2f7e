package com.homethy.model;

import com.homethy.enums.TenancyStatus;
import com.homethy.enums.TenancyType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Tenancy entity
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "tenancy")
public class Tenancy extends BaseEntity {

    @Column(name = "reference", nullable = false)
    private String reference;

    @Column(name = "address")
    private String address;

    @Column(name = "address_line2")
    private String addressLine2;

    @Column(name = "country")
    private String country;

    @Column(name = "postcode")
    private String postcode;

    @Column(name = "renewal_date")
    private LocalDateTime renewalDate;

    @Column(name = "period")
    private String period;

    @Column(name = "rent", precision = 10, scale = 2)
    private BigDecimal rent;

    @Column(name = "is_rent_guarantee_required")
    private Boolean isRentGuaranteeRequired;

    @Column(name = "tenancy_property_id", nullable = false)
    private String tenancyPropertyId;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private TenancyStatus status;

    @Column(name = "number")
    private String number;

    @Column(name = "title")
    private String title;

    @Column(name = "invoice_start_date")
    private LocalDateTime invoiceStartDate;

    @Column(name = "start_date", nullable = false)
    private LocalDateTime startDate;

    @Column(name = "end_date")
    private LocalDateTime endDate;

    @Column(name = "expected_exchange_date")
    private LocalDateTime expectedExchangeDate;

    @Column(name = "rent_review_date")
    private LocalDateTime rentReviewDate;

    @Column(name = "review_notice")
    private LocalDateTime reviewNotice;

    @Column(name = "break_clause_date")
    private LocalDateTime breakClauseDate;

    @Column(name = "rent_review")
    private LocalDateTime rentReview;

    @Column(name = "payment_day")
    private Integer paymentDay;

    @Column(name = "deposit", precision = 10, scale = 2)
    private BigDecimal deposit;

    @Column(name = "transferred_date")
    private LocalDateTime transferredDate;

    @Column(name = "refunded_date")
    private LocalDateTime refundedDate;

    @Column(name = "reservation", precision = 10, scale = 2)
    private BigDecimal reservation;

    @Column(name = "rent_increase_amount", precision = 10, scale = 2)
    private BigDecimal rentIncreaseAmount;

    @Column(name = "tenant_break_clause_months")
    private Integer tenantBreakClauseMonths;

    @Column(name = "landlord_break_clause_months")
    private Integer landlordBreakClauseMonths;

    @Column(name = "asking_price", precision = 10, scale = 2)
    private BigDecimal askingPrice;

    @Column(name = "agreed_price", precision = 10, scale = 2)
    private BigDecimal agreedPrice;

    @Column(name = "sale_admin_fee", precision = 10, scale = 2)
    private BigDecimal saleAdminFee;

    @Column(name = "rent_note", columnDefinition = "TEXT")
    private String rentNote;

    @Column(name = "rent_increase_date")
    private LocalDateTime rentIncreaseDate;

    @Column(name = "accounting")
    private Boolean accounting;

    @Column(name = "landlord_vat")
    private Boolean landlordVat;

    @Column(name = "auto_invoice_rent")
    private Boolean autoInvoiceRent;

    @Column(name = "add_landlord_details")
    private Boolean addLandlordDetails;

    @Column(name = "attach_invoices_to_contract")
    private Boolean attachInvoicesToContract;

    @Column(name = "share_invoices_to_contract")
    private Boolean shareInvoicesToContract;

    @Column(name = "share_invoices_with_landlord")
    private Boolean shareInvoicesWithLandlord;

    @Column(name = "deposit_reference")
    private String depositReference;

    @Enumerated(EnumType.STRING)
    @Column(name = "type")
    private TenancyType type;

    @Column(name = "certificate_number")
    private String certificateNumber;

    @Column(name = "deposit_protection_scheme")
    private String depositProtectionScheme;

    @Column(name = "date_deposit_registered")
    private LocalDateTime dateDepositRegistered;

    @Column(name = "deposit_returned")
    private Boolean depositReturned;

    @Column(name = "deposit_released")
    private Boolean depositReleased;

    @Column(name = "deposit_registered")
    private Boolean depositRegistered;

    @Column(name = "deposit_transferred")
    private Boolean depositTransferred;

    @Column(name = "deposit_disputed")
    private Boolean depositDisputed;

    @Column(name = "deposit_selected_scheme")
    private String depositSelectedScheme;

    @Column(name = "deposit_status")
    private String depositStatus;

    @Column(name = "opening_balance", precision = 10, scale = 2)
    private BigDecimal openingBalance;

    @Column(name = "archived")
    private Boolean archived;

    @Column(name = "last_action")
    private String lastAction;

    @Column(name = "area", precision = 10, scale = 2)
    private BigDecimal area;

    @Column(name = "tenancy_organisation_id", nullable = false)
    private String tenancyOrganisationId;

    @Column(name = "tenancy_settings_id")
    private String tenancySettingsId;

    @Column(name = "dont_collect_rent")
    private Boolean dontCollectRent;

    @Column(name = "enable_journal")
    private Boolean enableJournal;

    @Column(name = "enable_pro_rata_journal")
    private Boolean enableProRataJournal;

    @Column(name = "primary_tenant")
    private String primaryTenant;

    @Column(name = "primary_tenant_name")
    private String primaryTenantName;

    @ElementCollection
    @CollectionTable(name = "tenancy_tags", joinColumns = @JoinColumn(name = "tenancy_id"))
    @Column(name = "tag")
    private List<String> tags;

    @ElementCollection
    @CollectionTable(name = "tenancy_applicants", joinColumns = @JoinColumn(name = "tenancy_id"))
    @Column(name = "applicant_id")
    private List<String> applicants;

    @ElementCollection
    @CollectionTable(name = "tenancy_tenants", joinColumns = @JoinColumn(name = "tenancy_id"))
    @Column(name = "tenant_id")
    private List<String> tenants;

    @ElementCollection
    @CollectionTable(name = "tenancy_team_members", joinColumns = @JoinColumn(name = "tenancy_id"))
    @Column(name = "team_member_id")
    private List<String> teamMembers;

    @ElementCollection
    @CollectionTable(name = "tenancy_landlords", joinColumns = @JoinColumn(name = "tenancy_id"))
    @Column(name = "landlord_id")
    private List<String> landlords;
}
