package com.homethy.dao;

import com.homethy.model.Tenancy;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Tenancy repository
 */
@Repository
public interface TenancyRepository extends JpaRepository<Tenancy, String> {

    /**
     * Find tenancy by ID and not deleted
     */
    @Query("SELECT t FROM Tenancy t WHERE t.id = :id AND t.deleteFlag = 0")
    Optional<Tenancy> findByIdAndNotDeleted(@Param("id") String id);

    /**
     * Find tenancies by organisation ID and not deleted
     */
    @Query("SELECT t FROM Tenancy t WHERE t.tenancyOrganisationId = :organisationId AND t.deleteFlag = 0")
    List<Tenancy> findByOrganisationIdAndNotDeleted(@Param("organisationId") String organisationId);

    /**
     * Find tenancy by reference and organisation ID and not deleted
     */
    @Query("SELECT t FROM Tenancy t WHERE t.reference = :reference AND t.tenancyOrganisationId = :organisationId AND t.deleteFlag = 0")
    Optional<Tenancy> findByReferenceAndOrganisationIdAndNotDeleted(@Param("reference") String reference, @Param("organisationId") String organisationId);

    /**
     * Check if reference exists in organisation
     */
    @Query("SELECT COUNT(t) > 0 FROM Tenancy t WHERE t.reference = :reference AND t.tenancyOrganisationId = :organisationId AND t.deleteFlag = 0")
    boolean existsByReferenceAndOrganisationId(@Param("reference") String reference, @Param("organisationId") String organisationId);
}
