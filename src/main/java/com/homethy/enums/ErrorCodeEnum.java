package com.homethy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Error code enumeration
 */
@Getter
@AllArgsConstructor
public enum ErrorCodeEnum {

    SUCCESS(0, "成功"),
    SYSTEM_ERROR(1000, "系统错误"),
    PARAMETER_ERROR(1001, "参数错误"),
    RESOURCE_NOT_FOUND(1002, "资源不存在"),
    TENANCY_NOT_FOUND(2001, "租约不存在"),
    TENANCY_SETTINGS_NOT_FOUND(2002, "租约设置不存在"),
    TENANCY_COPY_FAILED(2003, "租约复制失败"),
    REFERENCE_GENERATION_FAILED(2004, "引用编号生成失败");

    private final Integer code;
    private final String message;
}
