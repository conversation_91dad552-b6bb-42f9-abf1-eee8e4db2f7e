package com.homethy.model.dto;

import com.homethy.enums.TenancyStatus;
import com.homethy.enums.TenancyType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Tenancy response DTO
 */
@Data
public class TenancyResponse {

    private String id;
    private String reference;
    private String address;
    private String addressLine2;
    private String country;
    private String postcode;
    private LocalDateTime renewalDate;
    private String period;
    private BigDecimal rent;
    private Boolean isRentGuaranteeRequired;
    private String tenancyPropertyId;
    private TenancyStatus status;
    private String number;
    private String title;
    private LocalDateTime invoiceStartDate;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private LocalDateTime expectedExchangeDate;
    private LocalDateTime rentReviewDate;
    private LocalDateTime reviewNotice;
    private LocalDateTime breakClauseDate;
    private LocalDateTime rentReview;
    private Integer paymentDay;
    private BigDecimal deposit;
    private LocalDateTime transferredDate;
    private LocalDateTime refundedDate;
    private BigDecimal reservation;
    private BigDecimal rentIncreaseAmount;
    private Integer tenantBreakClauseMonths;
    private Integer landlordBreakClauseMonths;
    private BigDecimal askingPrice;
    private BigDecimal agreedPrice;
    private BigDecimal saleAdminFee;
    private String rentNote;
    private LocalDateTime rentIncreaseDate;
    private Boolean accounting;
    private Boolean landlordVat;
    private Boolean autoInvoiceRent;
    private Boolean addLandlordDetails;
    private Boolean attachInvoicesToContract;
    private Boolean shareInvoicesToContract;
    private Boolean shareInvoicesWithLandlord;
    private String depositReference;
    private TenancyType type;
    private String certificateNumber;
    private String depositProtectionScheme;
    private LocalDateTime dateDepositRegistered;
    private Boolean depositReturned;
    private Boolean depositReleased;
    private Boolean depositRegistered;
    private Boolean depositTransferred;
    private Boolean depositDisputed;
    private String depositSelectedScheme;
    private String depositStatus;
    private BigDecimal openingBalance;
    private Boolean archived;
    private String lastAction;
    private BigDecimal area;
    private String tenancyOrganisationId;
    private String tenancySettingsId;
    private Boolean dontCollectRent;
    private Boolean enableJournal;
    private Boolean enableProRataJournal;
    private String primaryTenant;
    private String primaryTenantName;
    private List<String> tags;
    private List<String> applicants;
    private List<String> tenants;
    private List<String> teamMembers;
    private List<String> landlords;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
