server:
  port: 8080

spring:
  application:
    name: tenancy-test
  datasource:
    url: *************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  jackson:
    date-format: yyyy-MM-dd'T'HH:mm:ss'Z'
    time-zone: UTC

logging:
  level:
    com.homethy: DEBUG
    org.springframework.web: DEBUG
  pattern:
    console: "[%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}]|%level|[%X{traceId:-},%X{spanId:-}]|[%thread]|%logger{36}:%line|%msg%n"
