package com.homethy.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Bean copy utility
 */
@Slf4j
public class BeanCopyUtil {

    /**
     * Copy properties from source to target class
     *
     * @param source      Source object
     * @param targetClass Target class
     * @param <T>         Target type
     * @return Target object
     */
    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            log.error("Error copying properties from {} to {}: {}", 
                    source.getClass().getSimpleName(), targetClass.getSimpleName(), e.getMessage(), e);
            throw new RuntimeException("Failed to copy properties", e);
        }
    }

    /**
     * Copy properties from source to target object
     *
     * @param source Source object
     * @param target Target object
     */
    public static void copyProperties(Object source, Object target) {
        if (source == null || target == null) {
            return;
        }
        try {
            BeanUtils.copyProperties(source, target);
        } catch (Exception e) {
            log.error("Error copying properties from {} to {}: {}", 
                    source.getClass().getSimpleName(), target.getClass().getSimpleName(), e.getMessage(), e);
            throw new RuntimeException("Failed to copy properties", e);
        }
    }
}
