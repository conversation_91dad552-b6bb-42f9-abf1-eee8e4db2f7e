package com.homethy.exception;

import com.homethy.enums.ErrorCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Custom business exception
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListingHubException extends RuntimeException {

    private Integer code;
    private String message;

    public ListingHubException(ErrorCodeEnum errorCode) {
        super(errorCode.getMessage());
        this.code = errorCode.getCode();
        this.message = errorCode.getMessage();
    }

    public ListingHubException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public ListingHubException(String message) {
        super(message);
        this.code = 1;
        this.message = message;
    }

    public ListingHubException(String message, Throwable cause) {
        super(message, cause);
        this.code = 1;
        this.message = message;
    }
}
