package com.homethy.controller;

import com.homethy.model.dto.ApiResponse;
import com.homethy.model.dto.TenancyResponse;
import com.homethy.service.TenancyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2025-07-15
 * @Description Tenancy controller
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/tenancies")
@RequiredArgsConstructor
@Validated
public class TenancyController {

    private final TenancyService tenancyService;

    /**
     * Copy a tenancy
     * Creates an exact copy of an existing tenancy with a new unique ID and updated timestamps.
     * The copied tenancy will have DRAFT status and include all original data except for system-generated fields.
     *
     * @param organisationId The ID of the organisation that owns the tenancy
     * @param tenancyId      The ID of the tenancy to copy
     * @return Copied tenancy response
     */
    @PostMapping("/{organisationId}/{tenancyId}/copy")
    public ResponseEntity<ApiResponse<TenancyResponse>> copyTenancy(
            @PathVariable @NotBlank(message = "Organisation ID is required") String organisationId,
            @PathVariable @NotBlank(message = "Tenancy ID is required") String tenancyId) {

        log.info("Received request to copy tenancy {} for organisation {}", tenancyId, organisationId);

        TenancyResponse copiedTenancy = tenancyService.copyTenancy(organisationId, tenancyId);

        log.info("Successfully copied tenancy {} to new tenancy {}", tenancyId, copiedTenancy.getId());

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("租约复制成功", copiedTenancy));
    }
}
