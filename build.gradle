plugins {
    id 'java'
    id 'org.springframework.boot' version '3.1.0'
    id 'io.spring.dependency-management' version '1.1.0'
    id 'checkstyle'
    id 'com.github.spotbugs' version '5.0.13'
}

group = 'com.homethy'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '17'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    
    // Database
    runtimeOnly 'mysql:mysql-connector-java:8.0.33'
    
    // Lombok
    compileOnly 'org.projectlombok:lombok:1.18.32'
    annotationProcessor 'org.projectlombok:lombok:1.18.32'
    
    // JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.13.4.2'
    
    // Utility libraries
    implementation 'cn.hutool:hutool-all:5.8.25'
    implementation 'com.google.guava:guava:33.0.0-jre'
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    
    // Test dependencies
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.jmockit:jmockit:1.49'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2022.0.3"
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

// Checkstyle configuration
checkstyle {
    toolVersion = '10.3.1'
    configFile = file('config/checkstyle/checkstyle.xml')
    ignoreFailures = false
    maxWarnings = 0
}

checkstyleMain {
    source = 'src/main/java'
}

checkstyleTest {
    source = 'src/test/java'
}

// SpotBugs configuration
spotbugs {
    ignoreFailures = false
    effort = 'max'
    reportLevel = 'low'
}

spotbugsMain {
    reports {
        html {
            enabled = true
            destination = file("$buildDir/reports/spotbugs/main/spotbugs.html")
        }
    }
}

spotbugsTest {
    reports {
        html {
            enabled = true
            destination = file("$buildDir/reports/spotbugs/test/spotbugs.html")
        }
    }
}

// Java compilation options
compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs += ['-Xlint:unchecked', '-Xlint:deprecation']
}

compileTestJava {
    options.encoding = 'UTF-8'
}

// JAR configuration
jar {
    enabled = false
}

bootJar {
    archiveFileName = "${project.name}-${project.version}.jar"
}

// Task to download checkstyle configuration
task downloadCheckstyleConfig {
    doLast {
        def configDir = file('config/checkstyle')
        configDir.mkdirs()
        def configFile = file('config/checkstyle/checkstyle.xml')
        if (!configFile.exists()) {
            new URL('https://gitlab.w.chime.me/chime/checkstyle/raw/master/checkstyle.xml').withInputStream { i ->
                configFile.withOutputStream { it << i }
            }
        }
    }
}

// Make checkstyle depend on downloading config
checkstyleMain.dependsOn downloadCheckstyleConfig
checkstyleTest.dependsOn downloadCheckstyleConfig
